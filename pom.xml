<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.chinastock</groupId>
        <artifactId>galaxy-boot-parent</artifactId>
        <version>0.4.3</version>
    </parent>

    <artifactId>galaxy-boot-playbook-webflux</artifactId>
    <version>1.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-swagger-webflux</artifactId>
        </dependency>

        <!-- Jackson for JSON processing -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- OkHttp for HTTP client -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>

        <!-- OkHttp SSE support -->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp-sse</artifactId>
        </dependency>

        <!-- Redis 依赖 -->
        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-redis</artifactId>
        </dependency>
        <!-- Kafka 依赖 -->
        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-kafka</artifactId>
        </dependency>
        <!-- Cache 依赖 -->
        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-cache</artifactId>
        </dependency>
        <!-- Metrics 依赖 -->
        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-metrics</artifactId>
        </dependency>
        <!-- Webclient 依赖 -->
        <dependency>
            <groupId>cn.com.chinastock</groupId>
            <artifactId>galaxy-boot-starter-webclient</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <phase>package</phase>
                        <goals>
                            <!-- spring-boot-maven-plugin 会将依赖打包到一个可执行 fat jar -->
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>