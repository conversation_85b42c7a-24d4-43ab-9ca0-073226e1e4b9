spring:
  cloud:
    loadbalancer:
      enabled: true
    discovery:
      client:
        simple:
          instances:
            loadbalancer-service:                # 服务名称
              - uri: http://localhost:8089  # 实例1地址
            bdp-service:
              - uri: http://localhost:8089

  # Redis 配置
  data:
    redis:
#      host: localhost
#      port: 6379
#      password:
#      database: 0
#      timeout: 2000ms
#      connect-timeout: 2000ms
#      lettuce:
#        pool:
#          enabled: false

  # Kafka 配置
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: my-group
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: true

    listener:
      type: single

    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      acks: all

galaxy:
  kafka:
    enabled: false
    log:
      enabled: true
      max-detail-records-count: 10

  webclient:
    esb:
      user: user
      password: password
