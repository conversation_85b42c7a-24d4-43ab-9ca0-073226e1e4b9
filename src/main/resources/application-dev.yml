spring:
  cloud:
    loadbalancer:
      enabled: true
    discovery:
      client:
        simple:
          instances:
            playbook-service:                # 服务名称
              - uri: http://cnf-galaxy-boot-playbook.tsxcph.chinastock.com.cn
            bdp-service:
              - uri: http://bdp-jdk21.tsph.chinastock.com.cn
            esb-service:
              - uri: http://10.4.27.52:22112/apiJson/V2/ctc

  # Redis配置
  data:
    redis:
      password: yhplatarch@redistest
      database: 14
      sentinel:
        master: mymaster
        nodes:
          - 10.4.27.28:27001  # Sentinel 节点1
          - 10.4.27.29:27001  # Sentinel 节点2
          - 10.4.27.30:27001  # Sentinel 节点3

galaxy:
  kafka:
    enable: true
    username: cnfcenterx
    password: k6g7f4d9n5d
    server:
      nodes: 10.4.27.19:9092,10.4.27.20:9092,10.4.27.25:9092,10.4.53.25:9092,10.4.53.26:9092,10.4.53.27:9092,10.4.53.28:9092,10.4.53.29:9092
    log:
      enabled: true
      max-detail-records-count: 10
    jaas:
      enable: true
    consumer:
      batch.listener: false
      is.ack: false
      enable.auto.commit: true
    max:
      block:
        ms: 5000

  webclient:
    esb:
      user: user
      password: password