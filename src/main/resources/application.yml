server:
  port: 8088
  address: 0.0.0.0

spring:
  application:
    name: galaxy-boot-webflux-example

  profiles:
    active: local

  # Spring Boot 原生的 Caffeine 默认配置
  cache:
    type: caffeine
    caffeine:
      # 这是一个全局的、默认的缓存配置
      # 所有未被特殊指定的配置项都将从此继承
      spec: > # 使用 spec 字符串进行详细配置，非常灵活
        maximumSize=500,
        expireAfterWrite=5s,
        recordStats
  # 自定义的、按缓存名区分的特殊配置
  custom-caffeine:
    specs:
      # 'products' 的特殊配置，超时时间30s
      products:
        spec: expireAfterWrite=30s

galaxy:
  system:
    code: CNF

  log:
    request-response:
      enabled: true
    performance:
      enabled: true
    exception-pretty-print: true

  ai:
    enabled: true
    base-url: http://copilot.tsolph.chinastock.com.cn
    system-id: sys_cnf
    system-secret: VsJjm+PmoFn+VjF6-6bI1R9yU9ornxJ5
    account: shixiaolong_it
    app-id: app-5D55owR6HjNVPfCvsaYQKJ
    connect-timeout: 10000
    read-timeout: 60000
    write-timeout: 60000

  swagger:
    auth:
      username: admin     # Swagger UI 访问用户名
      password: P@5swOrd  # Swagger UI 访问密码

# actuator & metrics相关配置
management:
  endpoints:
    web:
      base-path: /actuator
      exposure:
        include: prometheus

  metrics:
    tags:
      application: ${spring.application.name}
      region: my-region

  prometheus:
    metrics:
      export:
        enabled: true

logging:
  level:
    com.ctrip.framework: OFF
    org.apache.kafka: ERROR
    org.springframework.kafka: ERROR
