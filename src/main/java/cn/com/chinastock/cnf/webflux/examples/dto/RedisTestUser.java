package cn.com.chinastock.cnf.webflux.examples.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 测试用户模型
 * 
 * <AUTHOR>
 */
public class RedisTestUser implements Serializable {
    
    @Serial
    private static final long serialVersionUID = 1L;
    
    private Long id;
    private String name;
    private String email;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    private List<String> tags;
    private Map<String, Object> metadata;
    
    /**
     * 默认构造函数.
     */
    public RedisTestUser() {
    }

    /**
     * 构造函数.
     *
     * @param id 用户ID
     * @param name 用户名
     * @param email 邮箱
     */
    public RedisTestUser(Long id, String name, String email) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 获取用户ID.
     *
     * @return 用户ID
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置用户ID.
     *
     * @param id 用户ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取用户名.
     *
     * @return 用户名
     */
    public String getName() {
        return name;
    }

    /**
     * 设置用户名.
     *
     * @param name 用户名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取邮箱.
     *
     * @return 邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 设置邮箱.
     *
     * @param email 邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 获取创建时间.
     *
     * @return 创建时间
     */
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间.
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取标签列表.
     *
     * @return 标签列表
     */
    public List<String> getTags() {
        return tags;
    }

    /**
     * 设置标签列表.
     *
     * @param tags 标签列表
     */
    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    /**
     * 获取元数据.
     *
     * @return 元数据
     */
    public Map<String, Object> getMetadata() {
        return metadata;
    }

    /**
     * 设置元数据.
     *
     * @param metadata 元数据
     */
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
    
    @Override
    public String toString() {
        return "TestUser{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", createTime=" + createTime +
                ", tags=" + tags +
                ", metadata=" + metadata +
                '}';
    }
}
