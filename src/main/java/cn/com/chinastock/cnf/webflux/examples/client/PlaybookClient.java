package cn.com.chinastock.cnf.webflux.examples.client;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.webclient.WebClientExchange;
import org.springframework.web.service.annotation.GetExchange;
import reactor.core.publisher.Mono;

@WebClientExchange(name = "playbook-service", url = "http://localhost:8089")
public interface PlaybookClient {

    @GetExchange("/api/test/exception/forbidden")
    Mono<BaseResponse<Object>> forbidden();

    @GetExchange("/api/test/exception/not-found")
    Mono<BaseResponse<Object>> notFound();

    @GetExchange("/api/test/esb/timeout")
    Mono<String> timeout();
}
