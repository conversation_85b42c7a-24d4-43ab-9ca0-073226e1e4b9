package cn.com.chinastock.cnf.webflux.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webflux.examples.dto.RedisTestResult;
import cn.com.chinastock.cnf.webflux.examples.dto.RedisTestUser;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.*;

/**
 * Redis 操作测试服务 - WebFlux版本
 *
 * <AUTHOR>
 */
@Service
public class RedisOperationService {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(RedisOperationService.class);

    private static final String KEY_PREFIX = "CNF:";
    private final ReactiveRedisTemplate<String, Object> reactiveRedisTemplate;

    public RedisOperationService(ReactiveRedisTemplate<String, Object> reactiveRedisTemplate) {
        this.reactiveRedisTemplate = reactiveRedisTemplate;
    }

    /**
     * 测试 String 操作.
     *
     * @return 测试结果
     */
    public Mono<RedisTestResult> testStringOperations() {
        long startTime = System.currentTimeMillis();
        String keyPrefix = KEY_PREFIX + "test:string:" + System.currentTimeMillis() + ":";

        // SET 操作
        String key = keyPrefix + "simple";
        String valueToSet = "Hello Redis";
        logger.info("testStringOperations, Set: {} = {}", key, valueToSet);
        
        return reactiveRedisTemplate.opsForValue().set(key, valueToSet)
                .then(reactiveRedisTemplate.opsForValue().get(key))
                .map(value -> (String) value)
                .flatMap(value -> {
                    // SETEX 操作（带过期时间）
                    String expireKey = keyPrefix + "expire";
                    logger.info("testStringOperations, SetEX: {} = {} (10 seconds)", expireKey, "Will expire");
                    return reactiveRedisTemplate.opsForValue().set(expireKey, "Will expire", Duration.ofSeconds(10))
                            .then(reactiveRedisTemplate.getExpire(expireKey))
                            .flatMap(ttl -> {
                                // INCR 操作
                                String counterKey = keyPrefix + "counter";
                                logger.info("testStringOperations, Set: {} = 0", counterKey);
                                return reactiveRedisTemplate.opsForValue().set(counterKey, 0)
                                        .then(reactiveRedisTemplate.opsForValue().increment(counterKey))
                                        .map(counter -> {
                                            long executionTime = System.currentTimeMillis() - startTime;
                                            Map<String, Object> data = new HashMap<>();
                                            data.put("getValue", value);
                                            data.put("ttl", ttl);
                                            data.put("counter", counter);
                                            
                                            RedisTestResult result = RedisTestResult.success("String 操作测试成功", data);
                                            result.setExecutionTime(executionTime);
                                            return result;
                                        });
                            });
                })
                .doOnError(error -> logger.error("String 操作测试失败", error))
                .onErrorResume(error -> Mono.just(RedisTestResult.failure("String 操作测试失败", error)));
    }

    /**
     * 测试 Hash 操作.
     *
     * @return 测试结果
     */
    public Mono<RedisTestResult> testHashOperations() {
        long startTime = System.currentTimeMillis();
        String key = KEY_PREFIX + "test:hash:" + System.currentTimeMillis();

        // HSET 操作
        logger.info("testHashOperations, HSET: {} field={} value={}", key, "field1", "value1");
        
        return reactiveRedisTemplate.opsForHash().put(key, "field1", "value1")
                .then(reactiveRedisTemplate.opsForHash().put(key, "field2", "value2"))
                .then(reactiveRedisTemplate.opsForHash().get(key, "field1"))
                .flatMap(field1Value -> {
                    // HGETALL 操作
                    logger.info("testHashOperations, HGETALL: {}", key);
                    return reactiveRedisTemplate.opsForHash().entries(key)
                            .collectMap(entry -> entry.getKey(), entry -> entry.getValue())
                            .flatMap(allFields -> {
                                // HDEL 操作
                                logger.info("testHashOperations, HDEL: {} field={}", key, "field2");
                                return reactiveRedisTemplate.opsForHash().remove(key, "field2")
                                        .then(reactiveRedisTemplate.opsForHash().hasKey(key, "field2"))
                                        .map(field2Exists -> {
                                            long executionTime = System.currentTimeMillis() - startTime;
                                            Map<String, Object> data = new HashMap<>();
                                            data.put("field1Value", field1Value);
                                            data.put("allFieldsCount", allFields.size());
                                            data.put("field2Exists", field2Exists);
                                            
                                            RedisTestResult result = RedisTestResult.success("Hash 操作测试成功", data);
                                            result.setExecutionTime(executionTime);
                                            return result;
                                        });
                            });
                })
                .doOnError(error -> logger.error("Hash 操作测试失败", error))
                .onErrorResume(error -> Mono.just(RedisTestResult.failure("Hash 操作测试失败", error)));
    }

    /**
     * 测试 List 操作.
     *
     * @return 测试结果
     */
    public Mono<RedisTestResult> testListOperations() {
        long startTime = System.currentTimeMillis();
        String key = KEY_PREFIX + "test:list:" + System.currentTimeMillis();

        // LPUSH 操作
        logger.info("testListOperations, LPUSH: {} value={}", key, "item1");
        
        return reactiveRedisTemplate.opsForList().leftPush(key, "item1")
                .then(reactiveRedisTemplate.opsForList().leftPush(key, "item2"))
                .then(reactiveRedisTemplate.opsForList().rightPush(key, "item3"))
                .then(reactiveRedisTemplate.opsForList().range(key, 0, -1).collectList())
                .flatMap(allItems -> {
                    // LPOP 操作
                    logger.info("testListOperations, LPOP: {}", key);
                    return reactiveRedisTemplate.opsForList().leftPop(key)
                            .flatMap(leftPoppedItem -> {
                                // 获取列表长度
                                logger.info("testListOperations, SIZE: {}", key);
                                return reactiveRedisTemplate.opsForList().size(key)
                                        .map(listSize -> {
                                            long executionTime = System.currentTimeMillis() - startTime;
                                            Map<String, Object> data = new HashMap<>();
                                            data.put("allItems", allItems);
                                            data.put("leftPoppedItem", leftPoppedItem);
                                            data.put("finalSize", listSize);
                                            
                                            RedisTestResult result = RedisTestResult.success("List 操作测试成功", data);
                                            result.setExecutionTime(executionTime);
                                            return result;
                                        });
                            });
                })
                .doOnError(error -> logger.error("List 操作测试失败", error))
                .onErrorResume(error -> Mono.just(RedisTestResult.failure("List 操作测试失败", error)));
    }

    /**
     * 测试 Set 操作.
     *
     * @return 测试结果
     */
    public Mono<RedisTestResult> testSetOperations() {
        long startTime = System.currentTimeMillis();
        String key = KEY_PREFIX + "test:set:" + System.currentTimeMillis();

        // SADD 操作
        logger.info("testSetOperations, SADD: {} members={}", key, Arrays.asList("member1", "member2", "member3"));
        
        return reactiveRedisTemplate.opsForSet().add(key, "member1", "member2", "member3")
                .then(reactiveRedisTemplate.opsForSet().members(key).collectList())
                .flatMap(allMembers -> {
                    // SISMEMBER 操作
                    logger.info("testSetOperations, SISMEMBER: {} member={}", key, "member1");
                    return reactiveRedisTemplate.opsForSet().isMember(key, "member1")
                            .flatMap(isMember -> {
                                // SREM 操作
                                logger.info("testSetOperations, SREM: {} member={}", key, "member2");
                                return reactiveRedisTemplate.opsForSet().remove(key, "member2")
                                        .then(reactiveRedisTemplate.opsForSet().size(key))
                                        .map(setSize -> {
                                            long executionTime = System.currentTimeMillis() - startTime;
                                            Map<String, Object> data = new HashMap<>();
                                            data.put("allMembers", allMembers);
                                            data.put("isMember1", isMember);
                                            data.put("finalSize", setSize);
                                            
                                            RedisTestResult result = RedisTestResult.success("Set 操作测试成功", data);
                                            result.setExecutionTime(executionTime);
                                            return result;
                                        });
                            });
                })
                .doOnError(error -> logger.error("Set 操作测试失败", error))
                .onErrorResume(error -> Mono.just(RedisTestResult.failure("Set 操作测试失败", error)));
    }

    /**
     * 测试 ZSet 操作.
     *
     * @return 测试结果
     */
    public Mono<RedisTestResult> testZSetOperations() {
        long startTime = System.currentTimeMillis();
        String key = KEY_PREFIX + "test:zset:" + System.currentTimeMillis();

        // ZADD 操作
        logger.info("testZSetOperations, ZADD: {} member={} score={}", key, "member1", 1.0);
        
        return reactiveRedisTemplate.opsForZSet().add(key, "member1", 1.0)
                .then(reactiveRedisTemplate.opsForZSet().add(key, "member2", 2.0))
                .then(reactiveRedisTemplate.opsForZSet().add(key, "member3", 3.0))
                .then(reactiveRedisTemplate.opsForZSet().range(key, org.springframework.data.domain.Range.closed(0L, -1L)).collectList())
                .flatMap(rangeMembers -> {
                    // ZSCORE 操作
                    logger.info("testZSetOperations, ZSCORE: {} member={}", key, "member2");
                    return reactiveRedisTemplate.opsForZSet().score(key, "member2")
                            .flatMap(score -> {
                                // ZREM 操作
                                logger.info("testZSetOperations, ZREM: {} member={}", key, "member1");
                                return reactiveRedisTemplate.opsForZSet().remove(key, "member1")
                                        .then(reactiveRedisTemplate.opsForZSet().size(key))
                                        .map(zsetSize -> {
                                            long executionTime = System.currentTimeMillis() - startTime;
                                            Map<String, Object> data = new HashMap<>();
                                            data.put("rangeMembers", rangeMembers);
                                            data.put("member2Score", score);
                                            data.put("finalSize", zsetSize);
                                            
                                            RedisTestResult result = RedisTestResult.success("ZSet 操作测试成功", data);
                                            result.setExecutionTime(executionTime);
                                            return result;
                                        });
                            });
                })
                .doOnError(error -> logger.error("ZSet 操作测试失败", error))
                .onErrorResume(error -> Mono.just(RedisTestResult.failure("ZSet 操作测试失败", error)));
    }

    /**
     * 测试过期时间操作.
     *
     * @return 测试结果
     */
    public Mono<RedisTestResult> testExpirationOperations() {
        long startTime = System.currentTimeMillis();
        String key = KEY_PREFIX + "test:expiration:" + System.currentTimeMillis();

        // 设置带过期时间的键
        logger.info("testExpirationOperations, SET: {} value={} expire={}s", key, "expiring value", 5);
        
        return reactiveRedisTemplate.opsForValue().set(key, "expiring value", Duration.ofSeconds(5))
                .then(reactiveRedisTemplate.getExpire(key))
                .flatMap(ttl -> {
                    // 延长过期时间
                    logger.info("testExpirationOperations, EXPIRE: {} newExpire={}s", key, 10);
                    return reactiveRedisTemplate.expire(key, Duration.ofSeconds(10))
                            .then(reactiveRedisTemplate.getExpire(key))
                            .flatMap(newTtl -> {
                                // 移除过期时间
                                logger.info("testExpirationOperations, PERSIST: {}", key);
                                return reactiveRedisTemplate.persist(key)
                                        .then(reactiveRedisTemplate.getExpire(key))
                                        .map(persistTtl -> {
                                            long executionTime = System.currentTimeMillis() - startTime;
                                            Map<String, Object> data = Map.of(
                                                    "initialTtl", ttl,
                                                    "extendedTtl", newTtl,
                                                    "persistTtl", persistTtl
                                            );
                                            
                                            RedisTestResult result = RedisTestResult.success("过期时间操作测试成功", data);
                                            result.setExecutionTime(executionTime);
                                            return result;
                                        });
                            });
                })
                .doOnError(error -> logger.error("过期时间操作测试失败", error))
                .onErrorResume(error -> Mono.just(RedisTestResult.failure("过期时间操作测试失败", error)));
    }

    /**
     * 批量写入并批量读取String键值对测试.
     *
     * @return 测试结果
     */
    public Mono<RedisTestResult> testMultiSetAndGetOperations() {
        long startTime = System.currentTimeMillis();
        String keyPrefix = KEY_PREFIX + "test:multiSetAndGet:" + System.currentTimeMillis() + ":";
        Map<String, String> kvMap = new HashMap<>();
        for (int i = 0; i < 5; i++) {
            kvMap.put(keyPrefix + i, "value" + i);
        }
        logger.info("testMultiSetAndGetOperations, MSET: {}", kvMap);
        
        return reactiveRedisTemplate.opsForValue().multiSet(kvMap)
                .then(reactiveRedisTemplate.opsForValue().multiGet(new ArrayList<>(kvMap.keySet())))
                .map(values -> {
                    long executionTime = System.currentTimeMillis() - startTime;
                    Map<String, Object> data = new HashMap<>();
                    data.put("multiSetKeys", kvMap.keySet());
                    data.put("multiSetValues", kvMap.values());
                    data.put("multiGetValues", values);
                    RedisTestResult result = RedisTestResult.success("批量写入并读取成功", data);
                    result.setExecutionTime(executionTime);
                    return result;
                })
                .doOnError(error -> logger.error("批量写入并读取失败", error))
                .onErrorResume(error -> Mono.just(RedisTestResult.failure("批量写入并读取失败", error)));
    }

    /**
     * 运行所有 Redis 操作测试.
     *
     * @return 所有Redis操作测试结果列表
     */
    public Flux<RedisTestResult> runAllRedisOperationTests() {
        return Flux.concat(
                testStringOperations(),
                testHashOperations(),
                testListOperations(),
                testSetOperations(),
                testZSetOperations(),
                testMultiSetAndGetOperations(),
                testObjectSerialization()
        ).subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 测试对象序列化器（默认使用FastJson2）.
     *
     * @return 测试结果
     */
    public Mono<RedisTestResult> testObjectSerialization() {
        long startTime = System.currentTimeMillis();

        // 创建测试用户
        RedisTestUser user = new RedisTestUser(1L, "张三", "<EMAIL>");
        user.setTags(Arrays.asList("developer", "java", "redis"));
        user.setMetadata(Map.of("department", "IT", "level", "senior"));
        String key = KEY_PREFIX + "test:serialization:default:" + System.currentTimeMillis();

        // 存储对象
        logger.info("testObjectSerialization, SET: {}, value={}", key, user);
        
        return reactiveRedisTemplate.opsForValue().set(key, user, Duration.ofMillis(500))
                .then(reactiveRedisTemplate.opsForValue().get(key))
                .map(retrievedUser -> (RedisTestUser) retrievedUser)
                .map(retrievedUser -> {
                    // 验证数据完整性
                    boolean isValid = validateUser(user, retrievedUser);
                    long executionTime = System.currentTimeMillis() - startTime;

                    if (isValid) {
                        RedisTestResult result = RedisTestResult.success("默认序列化器测试成功", retrievedUser);
                        result.setExecutionTime(executionTime);
                        return result;
                    } else {
                        return RedisTestResult.failure("序列化后数据不一致");
                    }
                })
                .doOnError(error -> logger.error("默认序列化器测试失败", error))
                .onErrorResume(error -> Mono.just(RedisTestResult.failure("默认序列化器测试失败", error)));
    }

    private boolean validateUser(RedisTestUser original, RedisTestUser retrieved) {
        if (retrieved == null) return false;
        return Objects.equals(original.getId(), retrieved.getId()) &&
                Objects.equals(original.getName(), retrieved.getName()) &&
                Objects.equals(original.getEmail(), retrieved.getEmail());
    }
}
