package cn.com.chinastock.cnf.webflux.examples.client;

import java.util.Map;

import org.springframework.web.service.annotation.PostExchange;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.webclient.WebClientExchange;
import reactor.core.publisher.Mono;

@WebClientExchange(name = "bdp-service")
public interface BdpClient {

    @PostExchange(value = "/bdp_docker/userAsset/queryMyAssetDetail")
    Mono<BaseResponse<Object>> queryMyAssetDetail(Map req);

}
