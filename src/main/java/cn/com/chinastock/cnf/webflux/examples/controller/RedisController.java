package cn.com.chinastock.cnf.webflux.examples.controller;

import cn.com.chinastock.cnf.webflux.examples.dto.RedisTestResult;
import cn.com.chinastock.cnf.webflux.examples.service.RedisOperationService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * Redis操作测试控制器 - WebFlux版本
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/test/redis")
public class RedisController {

    /** Redis操作测试服务. */
    private final RedisOperationService redisOperationService;

    public RedisController(RedisOperationService redisOperationService) {
        this.redisOperationService = redisOperationService;
    }

    /**
     * 测试String操作.
     *
     * @return 测试结果
     */
    @PostMapping("/string")
    public Mono<RedisTestResult> testStringOperations() {
        return redisOperationService.testStringOperations();
    }

    /**
     * 测试Hash操作.
     *
     * @return 测试结果
     */
    @PostMapping("/hash")
    public Mono<RedisTestResult> testHashOperations() {
        return redisOperationService.testHashOperations();
    }

    /**
     * 测试List操作.
     *
     * @return 测试结果
     */
    @PostMapping("/list")
    public Mono<RedisTestResult> testListOperations() {
        return redisOperationService.testListOperations();
    }

    /**
     * 测试Set操作.
     *
     * @return 测试结果
     */
    @PostMapping("/set")
    public Mono<RedisTestResult> testSetOperations() {
        return redisOperationService.testSetOperations();
    }

    /**
     * 测试ZSet操作.
     *
     * @return 测试结果
     */
    @PostMapping("/zset")
    public Mono<RedisTestResult> testZSetOperations() {
        return redisOperationService.testZSetOperations();
    }

    /**
     * 测试过期操作.
     *
     * @return 测试结果
     */
    @PostMapping("/expiration")
    public Mono<RedisTestResult> testExpirationOperations() {
        return redisOperationService.testExpirationOperations();
    }

    /**
     * 批量写入并批量读取String操作测试.
     * @return 测试结果
     */
    @PostMapping("/multiSetAndGet")
    public Mono<RedisTestResult> testMultiSetAndGetOperations() {
        return redisOperationService.testMultiSetAndGetOperations();
    }

    /**
     * 运行所有Redis操作测试.
     *
     * @return 所有测试结果列表
     */
    @PostMapping("/operations/all")
    public Flux<RedisTestResult> runAllRedisOperationTests() {
        return redisOperationService.runAllRedisOperationTests();
    }

    /**
     * 测试默认序列化器（FastJson2）.
     *
     * @return 测试结果
     */
    @PostMapping("/serialization")
    public Mono<RedisTestResult> testDefaultSerialization() {
        return redisOperationService.testObjectSerialization();
    }

}
