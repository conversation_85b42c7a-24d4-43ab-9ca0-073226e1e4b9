package cn.com.chinastock.cnf.webflux.examples.consumer;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 单个消息消费者
 * 演示单条消息的消费处理
 */
@Component
public class KafkaMessageConsumer {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(KafkaMessageConsumer.class);

    /**
     * 消费单个消息 - 带元数据模式
     * 监听 single-message-with-metadata-topic 主题
     *
     * @param record 消费记录
     */
    @KafkaListener(topics = "cnf_notify_msg",
                   groupId = "single-metadata-webflux-group")
    public void consumeSingleMessageWithMetadata(ConsumerRecord<String, String> record) {
        String idempotencyId = getIdempotencyId(record);
        logger.info(LogCategory.APP_LOG,
                "Single message with metadata - Topic: {}, Partition: {}, Offset: {}, Idempotency ID: {}, Key: {}, Value: {}, Timestamp: {}",
                record.topic(), record.partition(), record.offset(), idempotencyId,
                record.key(), record.value(), record.timestamp());

        // 模拟业务处理
        processMessage(record.value(), "single-meta");
    }

    /**
     * 批量消费消息 - 带元数据模式
     * @param records 消费记录列表
     */
    @KafkaListener(topics = "cnf_notify_msg_batch",
                   groupId = "batch-metadata-webflux-group")
    public void consumeBatchMessagesWithMetadata(List<ConsumerRecord<String, String>> records) {
        logger.info(LogCategory.APP_LOG, "Batch consumer with metadata received {} records", records.size());

        for (int i = 0; i < records.size(); i++) {
            ConsumerRecord<String, String> record = records.get(i);
            String idempotencyId = getIdempotencyId(record);
            logger.info(LogCategory.APP_LOG,
                    "Processing batch record {}/{} - Topic: {}, Partition: {}, Offset: {}, Idempotency ID: {}, Key: {}, Value: {}",
                    i + 1, records.size(), record.topic(), record.partition(), record.offset(), idempotencyId,
                    record.key(), record.value());

            processBatchMessage(record.value(), i);
        }

        logger.info(LogCategory.APP_LOG, "Batch metadata processing completed for {} records", records.size());
    }

    private static String getIdempotencyId(ConsumerRecord<String, String> record) {
        String idempotencyId = null;
        if (record.headers() != null) {
            org.apache.kafka.common.header.Header idempotencyHeader = record.headers().lastHeader("idempotent_id");
            if (idempotencyHeader != null) {
                idempotencyId = new String(idempotencyHeader.value(), StandardCharsets.UTF_8);
            }
        }
        return idempotencyId;
    }

    /**
     * 通用消息处理方法
     *
     * @param message      消息内容
     * @param consumerType 消费者类型
     */
    private void processMessage(String message, String consumerType) {
        try {
            // 模拟业务处理逻辑
            Thread.sleep(10);
            logger.info(LogCategory.APP_LOG, "Message processed successfully by {}: {}", consumerType, message);
        } catch (InterruptedException e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Error in message processing", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 通用批量消息处理方法
     * @param message 消息内容
     * @param index 消息索引
     */
    private void processBatchMessage(String message, int index) {
        try {
            Thread.sleep(10);
            logger.debug(LogCategory.APP_LOG, "Batch message {} processed successfully: {}", index, message);
        } catch (InterruptedException e) {
            logger.error(LogCategory.EXCEPTION_LOG, "Error in batch message processing", e);
            Thread.currentThread().interrupt();
        }
    }
}
