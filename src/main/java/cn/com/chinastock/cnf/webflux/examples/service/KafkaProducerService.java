package cn.com.chinastock.cnf.webflux.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.webflux.examples.dto.KafkaBatchMessage;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.List;

/**
 * Kafka消息生产者服务 - WebFlux版本
 */
@Service
public class KafkaProducerService {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(KafkaProducerService.class);

    private final KafkaTemplate<String, String> kafkaTemplateForString;

    public KafkaProducerService(KafkaTemplate<String, String> kafkaTemplateForString, KafkaTemplate<Integer, String> kafkaTemplate) {
        this.kafkaTemplateForString = kafkaTemplateForString;
    }

    /**
     * 发送单个消息（使用String key）- WebFlux版本
     *
     * @return 发送结果的Mono
     */
    public Mono<SendResult<String, String>> sendMessage(String topic, String key, String message) {
        logger.info(LogCategory.APP_LOG, "Sending message to topic: {}, key: {}, message: {}",
                topic, key, message);

        return Mono.fromFuture(kafkaTemplateForString.send(topic, key, message))
                .subscribeOn(Schedulers.boundedElastic())
                .doOnSuccess(result -> logger.info(LogCategory.APP_LOG, "Message sent successfully to topic: {}, partition: {}, offset: {}",
                        result.getRecordMetadata().topic(),
                        result.getRecordMetadata().partition(),
                        result.getRecordMetadata().offset()))
                .doOnError(error -> logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to topic: " + topic, error));
    }

    /**
     * 批量发送消息 - WebFlux版本
     *
     * @param request 批量消息发送请求
     * @return 发送结果的Flux
     */
    public Flux<SendResult<String, String>> sendBatchMessages(KafkaBatchMessage request) {
        logger.info(LogCategory.APP_LOG, "Sending batch messages to topic: {}, count: {}",
                request.getTopic(), request.getMessages().size());

        return Flux.fromIterable(request.getMessages())
                .flatMap(item -> sendMessage(request.getTopic(), item.getKey(), item.getMessage()))
                .subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 批量发送消息 - 简化版本
     *
     * @param messages 消息列表
     * @param keyPrefix 消息键前缀
     * @param topic 主题
     * @return 发送结果的Mono
     */
    public Mono<Void> sendBatchMessages(List<String> messages, String keyPrefix, String topic) {
        logger.info(LogCategory.APP_LOG, "Sending {} messages to topic: {} with prefix: {}", 
                messages.size(), topic, keyPrefix);

        return Flux.fromIterable(messages)
                .index()
                .flatMap(tuple -> {
                    String message = tuple.getT2();
                    Long index = tuple.getT1();
                    String key = keyPrefix + "-" + System.currentTimeMillis() + "-" + index;
                    return sendMessage(topic, key, message);
                })
                .then()
                .subscribeOn(Schedulers.boundedElastic());
    }
}
