package cn.com.chinastock.cnf.webflux.examples.client;

import cn.com.chinastock.cnf.webclient.WebClientExchange;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.service.annotation.GetExchange;
import reactor.core.publisher.Mono;

@WebClientExchange(name = "loadbalancer-service")
public interface LoadBalancerClient {

    @GetExchange(value = "/api/test/esb/headers")
    Mono<String> headers(@RequestHeader("Function-No") String functionNo);

}
