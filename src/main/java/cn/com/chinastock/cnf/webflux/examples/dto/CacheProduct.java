package cn.com.chinastock.cnf.webflux.examples.dto;

import java.io.Serializable;

/**
 * 产品信息DTO
 */
public class CacheProduct implements Serializable {

    private String id;
    private String name;
    private String description;

    /**
     * 默认构造函数，用于序列化
     */
    public CacheProduct() {
    }

    /**
     * 全参构造函数
     */
    public CacheProduct(String id, String name, String description) {
        this.id = id;
        this.name = name;
        this.description = description;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
