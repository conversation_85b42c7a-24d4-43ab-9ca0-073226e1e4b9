package cn.com.chinastock.cnf.webflux.examples.client;

import java.util.Map;

import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.service.annotation.PostExchange;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.webclient.WebClientExchange;
import reactor.core.publisher.Mono;

@WebClientExchange(name = "esb-service", url = "http://localhost:8089")
public interface ESBClient {

    @PostExchange(value = "/")
    Mono<BaseResponse<Object>> queryServiceNo(@RequestHeader("Function-No") String functionNo, Map req);

}
