package cn.com.chinastock.cnf.webflux.examples.service;

import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webflux.examples.dto.CacheProduct;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 缓存产品服务 - WebFlux版本
 * <p>
 * 使用Spring Cache注解实现缓存操作
 */
@Service
public class CacheProductService {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(CacheProductService.class);

    /**
     * 获取产品信息（带缓存）
     *
     * @param id 产品ID
     * @return 产品信息对象的Mono
     */
    @Cacheable(cacheNames = "products", key = "#id")
    public Mono<CacheProduct> getProductById(String id) {
        logger.info("【WebFlux】正在查询产品，ID: {}", id);

        return Mono.fromCallable(() -> {
                    // 模拟耗时操作
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                    return new CacheProduct(id, "Product " + id, "Description for WebFlux product.");
                })
                .delayElement(Duration.ofMillis(100)) // 模拟数据库延迟
                .subscribeOn(Schedulers.boundedElastic())
                .doOnNext(product -> logger.info("【WebFlux】产品查询成功: {}", product.getName()))
                .doOnError(error -> logger.error("【WebFlux】产品查询失败: {}", error.getMessage()));
    }

    /**
     * 更新产品 - 更新缓存
     *
     * @param product 产品对象
     * @return Mono<CacheProduct>
     */
    @CachePut(cacheNames = "products", key = "#product.id")
    public Mono<CacheProduct> updateProduct(CacheProduct product) {
        logger.info("【WebFlux】更新产品: {}", product.getId());
        // 模拟数据库更新
        return Mono.just(product)
                .delayElement(Duration.ofMillis(300)) // 模拟网络延迟
                .subscribeOn(Schedulers.boundedElastic())
                .doOnNext(updatedProduct -> logger.info("【WebFlux】产品更新成功: {}", updatedProduct.getName()))
                .doOnError(error -> logger.error("【WebFlux】产品更新失败: {}", error.getMessage()));
    }

    /**
     * 删除产品 - 清除缓存
     *
     * @param id 产品ID
     * @return Mono<Boolean>
     */
    @CacheEvict(cacheNames = "products", key = "#id")
    public Mono<Boolean> deleteProduct(String id) {
        logger.info("【WebFlux】删除产品: {}", id);
        // 模拟数据库删除
        return Mono.just(true)
                .delayElement(Duration.ofMillis(200)) // 模拟网络延迟
                .subscribeOn(Schedulers.boundedElastic())
                .doOnNext(result -> logger.info("【WebFlux】产品删除成功: {}", id))
                .doOnError(error -> logger.error("【WebFlux】产品删除失败: {}", error.getMessage()));
    }

    /**
     * 清空所有缓存
     *
     * @return 操作结果
     */
    @CacheEvict(cacheNames = "products", allEntries = true)
    public Mono<Map<String, Object>> clearAllCache() {
        logger.info("【WebFlux】清空所有缓存");

        return Mono.fromCallable(() -> {
                    logger.info("【WebFlux】所有缓存已清空");
                    Map<String, Object> result = new HashMap<>();
                    result.put("success", true);
                    result.put("message", "所有缓存已清空");
                    return result;
                })
                .subscribeOn(Schedulers.boundedElastic())
                .doOnError(error -> logger.error("【WebFlux】清空缓存失败: {}", error.getMessage()))
                .onErrorResume(error -> Mono.just(Map.of(
                        "success", false,
                        "message", "清空失败: " + error.getMessage()
                )));
    }
}