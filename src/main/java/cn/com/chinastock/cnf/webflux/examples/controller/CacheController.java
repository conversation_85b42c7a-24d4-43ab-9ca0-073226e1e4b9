package cn.com.chinastock.cnf.webflux.examples.controller;

import cn.com.chinastock.cnf.webflux.examples.dto.CacheProduct;
import cn.com.chinastock.cnf.webflux.examples.service.CacheProductService;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 缓存操作控制器 - WebFlux版本
 * <p>
 * 提供缓存的增删改查操作接口
 */
@RestController
@RequestMapping("/api/test/cache")
public class CacheController {

    private final CacheProductService cacheProductService;

    public CacheController(CacheProductService cacheProductService) {
        this.cacheProductService = cacheProductService;
    }

    /**
     * 获取产品信息（带缓存）
     *
     * @param id 产品ID
     * @return 产品信息
     */
    @GetMapping("/products/{id}")
    public Mono<CacheProduct> getProduct(@PathVariable String id) {
        return cacheProductService.getProductById(id);
    }

    /**
     * 更新产品信息（更新缓存）
     *
     * @param product 产品信息
     * @return 更新后的产品信息
     */
    @PutMapping("/products")
    public Mono<CacheProduct> updateProduct(@RequestBody CacheProduct product) {
        return cacheProductService.updateProduct(product);
    }

    /**
     * 删除产品（清除缓存）
     *
     * @param id 产品ID
     * @return 删除结果
     */
    @DeleteMapping("/products/{id}")
    public Mono<Boolean> deleteProduct(@PathVariable String id) {
        return cacheProductService.deleteProduct(id);
    }

    /**
     * 清空所有缓存
     *
     * @return 操作结果
     */
    @DeleteMapping("/clear")
    public Mono<Map<String, Object>> clearAllCache() {
        return cacheProductService.clearAllCache();
    }

}