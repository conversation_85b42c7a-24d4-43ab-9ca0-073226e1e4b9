package cn.com.chinastock.cnf.webflux.examples.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.webclient.exception.GalaxyWebClientException;
import cn.com.chinastock.cnf.webflux.examples.client.BdpClient;
import cn.com.chinastock.cnf.webflux.examples.client.ESBClient;
import cn.com.chinastock.cnf.webflux.examples.client.LoadBalancerClient;
import cn.com.chinastock.cnf.webflux.examples.client.PlaybookClient;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test/webclient")
public class WebclientController {

    private final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(getClass());

    private final PlaybookClient playbookClient;

    private final ESBClient eSBClient;

    private final BdpClient bdpClient;

    private final LoadBalancerClient loadBalancerClient;

    public WebclientController(PlaybookClient playbookClient, ESBClient eSBClient, BdpClient bdpClient, LoadBalancerClient loadBalancerClient) {
        this.playbookClient = playbookClient;
        this.eSBClient = eSBClient;
        this.bdpClient = bdpClient;
        this.loadBalancerClient = loadBalancerClient;
    }

    @GetMapping("/timeout")
    public Mono<BaseResponse<Object>> timeout() {
        return playbookClient.timeout()
                .then(Mono.just(new BaseResponse<>(new Meta(true, "0", "success"), null)));
    }

    @GetMapping("/esb")
    public Mono<BaseResponse<Object>> callESB(@RequestParam(value = "serviceNo") String serviceNo) {
        Map<String, String> req = new HashMap<>();
        req.put("serviceNo", serviceNo);
        req.put("sysCode", "MPA");
        return eSBClient.queryServiceNo("YH0019010400001", req);
    }

    @GetMapping("/playbook-notfound")
    public Mono<BaseResponse<Object>> callNotFoundAPI() {
        return playbookClient.forbidden()
                .map(response -> new BaseResponse<>(new Meta(true, "0", "success"), null))
                .onErrorResume(GalaxyWebClientException.class, e -> {
                    logger.error("GalaxyWebClientException, code={}, message={}", e.getCode(), e.getMessage());
                    return Mono.just(new BaseResponse<>(e.getMeta(), null));
                });
    }

    @GetMapping("/galaxy-exception")
    public Mono<BaseResponse<Object>> callException() {
        return playbookClient.forbidden()
                .map(response -> new BaseResponse<>(new Meta(true, "0", "success"), null))
                .onErrorResume(GalaxyWebClientException.class, e -> {
                    logger.error("GalaxyWebClientException, code={}, message={}", e.getCode(), e.getMessage());
                    return Mono.just(new BaseResponse<>(e.getMeta(), null));
                });
    }

    @GetMapping("/bdp/queryMyAssetDetail")
    public Mono<BaseResponse<Object>> queryMyAssetDetail(@RequestParam(value = "custNo") String custNo) {
        Map<String, String> req = new HashMap<>();
        req.put("CustNo", custNo);
        return bdpClient.queryMyAssetDetail(req)
                .map(response -> new BaseResponse<>(new Meta(true, "0", "success"), response.getData()))
                .onErrorResume(throwable -> {
                    if (throwable instanceof GalaxyWebClientException e) {
                        return Mono.just(new BaseResponse<>(e.getMeta(), null));
                    }
                    return Mono.error(throwable);
                });
    }

    @GetMapping("/echo")
    public Mono<String> echo(@RequestParam String input) {
        return Mono.just("echo: " + input);
    }

    @GetMapping("/echo-body")
    public Mono<String> echoBody(@RequestBody Mono<String> input) {
        return input.map(s -> "echo: " + s);
    }

    @GetMapping("/loadbalancer")
    public Mono<BaseResponse<Object>> loadbalancer() {
        logger.info("loadbalancer");
        return loadBalancerClient.headers("YH0019010400001")
                .map(response -> new BaseResponse<Object>(new Meta(true, "0", "success"), response))
                .onErrorResume(GalaxyWebClientException.class, e -> {
                    logger.error("GalaxyWebClientException, code={}, message={}", e.getCode(), e.getMessage());
                    return Mono.just(new BaseResponse<Object>(e.getMeta(), null));
                });
    }
}
