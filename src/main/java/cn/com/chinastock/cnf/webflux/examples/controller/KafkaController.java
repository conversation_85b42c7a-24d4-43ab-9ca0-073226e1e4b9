package cn.com.chinastock.cnf.webflux.examples.controller;

import cn.com.chinastock.cnf.core.http.BaseResponse;
import cn.com.chinastock.cnf.core.http.Meta;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.webflux.examples.service.KafkaProducerService;
import org.springframework.kafka.support.SendResult;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Kafka消息发送控制器 - WebFlux版本
 */
@RestController
@RequestMapping("/api/test/kafka")
public class KafkaController {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(KafkaController.class);

    private final KafkaProducerService kafkaProducerService;

    public KafkaController(KafkaProducerService kafkaProducerService) {
        this.kafkaProducerService = kafkaProducerService;
    }

    /**
     * 发送消息到单个消息带元数据消费模式
     * 对应 SingleMessageConsumer.consumeSingleMessageWithMetadata()
     * @param message 消息内容
     * @param key 消息键（可选）
     * @return 发送结果响应
     */
    @PostMapping("/send-single-metadata")
    public Mono<BaseResponse<Map<String, Object>>> sendToSingleMetadata(
            @RequestParam String message,
            @RequestParam(required = false, defaultValue = "") String key) {
        
        String topic = "cnf_notify_msg";
        String messageKey = key.isEmpty() ? "metadata-" + System.currentTimeMillis() : key;

        return kafkaProducerService.sendMessage(topic, messageKey, message)
                .map(result -> {
                    Map<String, Object> responseData = buildResponseData(result, messageKey, "Single Message With Metadata Consumer");
                    return new BaseResponse<>(new Meta(true, "0", "Message sent to single metadata consumer successfully"), responseData);
                })
                .doOnError(error -> logger.error(LogCategory.EXCEPTION_LOG, "Failed to send message to single metadata consumer", error))
                .onErrorResume(error -> Mono.just(new BaseResponse<>(new Meta(false, "500", "Failed to send message: " + error.getMessage()), null)));
    }

    /**
     * 发送消息到批量消息带元数据消费模式
     * 对应 BatchMessageConsumer.consumeBatchMessagesWithMetadata()
     * @param messages 消息列表
     * @param keyPrefix 消息键前缀（可选）
     * @return 发送结果响应
     */
    @PostMapping("/send-batch-metadata")
    public Mono<BaseResponse<Map<String, Object>>> sendToBatchMetadata(
            @RequestBody List<String> messages,
            @RequestParam(required = false, defaultValue = "batch-metadata") String keyPrefix) {
        
        String topic = "cnf_notify_msg_batch";
        logger.info(LogCategory.APP_LOG, "Sending {} messages to batch metadata consumer", messages.size());

        return kafkaProducerService.sendBatchMessages(messages, keyPrefix, topic)
                .map(result -> {
                    Map<String, Object> responseData = buildBatchResponseData(messages, topic, "Batch Message With Metadata Consumer", "All messages sent successfully");
                    return new BaseResponse<>(new Meta(true, "0", "Messages sent to batch metadata consumer successfully"), responseData);
                })
                .doOnError(error -> logger.error(LogCategory.EXCEPTION_LOG, "Failed to send messages to batch metadata consumer", error))
                .onErrorResume(error -> Mono.just(new BaseResponse<>(new Meta(false, "500", "Failed to send messages: " + error.getMessage()), null)));
    }

    private static Map<String, Object> buildResponseData(SendResult<String, String> result, String key, String consumerMode) {
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("topic", result.getRecordMetadata().topic());
        responseData.put("partition", result.getRecordMetadata().partition());
        responseData.put("offset", result.getRecordMetadata().offset());
        responseData.put("key", key);
        responseData.put("consumerMode", consumerMode);
        return responseData;
    }

    private static Map<String, Object> buildBatchResponseData(List<String> messages, String topic, String consumerMode, String status) {
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("topic", topic);
        responseData.put("messageCount", messages.size());
        responseData.put("consumerMode", consumerMode);
        responseData.put("status", status);
        return responseData;
    }
}
