package cn.com.chinastock.cnf.webflux.examples.openai;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;

public class ContentToStringDeserializer extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonToken currentToken = p.currentToken();

        if (currentToken == JsonToken.VALUE_STRING) {
            return p.getText();
        }

        if (currentToken == JsonToken.START_ARRAY || currentToken == JsonToken.START_OBJECT) {
            JsonNode node = p.readValueAsTree();
            return node.toString();
        }

        ctxt.reportWrongTokenException(this, JsonToken.VALUE_STRING, "Attempted to deserialize a non-string, non-array, non-object token to a String.");
        return null;
    }
}